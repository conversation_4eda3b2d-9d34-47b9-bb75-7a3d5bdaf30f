// OccupancyMap.cpp
#include "OccupancyMap.h"
#include <algorithm> // for std::sort, std::remove_if

// 构造函数（第三步优化版本 - 添加容器预分配）
OccupancyMap::OccupancyMap(int height, int width, int depth, int time_buffer, int safety_envelope)
    : height_(height), width_(width), depth_(depth), time_buffer_(time_buffer), safety_envelope_(safety_envelope)
{
    // 基于地图尺寸进行智能预分配
    // 估算可能的最大占用点数（考虑安全包络）
    size_t estimated_max_points = static_cast<size_t>(height) * width * depth / 100; // 假设1%的空间会被占用
    size_t estimated_agents = 100;                                                   // 估算同时活跃的航班数量

    // 预分配容器空间
    reserve_capacity(estimated_agents, estimated_max_points);
}

// 私有辅助函数：在 occupancy_ 中添加新的时间区间，并处理合并逻辑
void OccupancyMap::add_or_merge_interval(const Point3D &point, const TimeInterval &new_interval)
{
    std::vector<TimeInterval> &intervals_at_point = occupancy_[point];

    // 检查是否可以与现有区间合并
    bool merged = false;
    for (TimeInterval &existing_interval : intervals_at_point)
    {
        if (existing_interval.can_merge_with(new_interval))
        {
            existing_interval = existing_interval.merged_with(new_interval);
            merged = true;
            // 在一次合并后，可能需要再次检查这个新合并的区间是否能与其他区间合并
            // 为了简化，我们先添加，然后统一排序和合并所有重叠区间
            break;
        }
    }

    if (!merged)
    {
        intervals_at_point.push_back(new_interval);
    }

    // 排序并合并所有重叠的区间
    std::sort(intervals_at_point.begin(), intervals_at_point.end());

    if (intervals_at_point.empty())
    {
        return;
    }

    std::vector<TimeInterval> merged_intervals;
    merged_intervals.push_back(intervals_at_point[0]);

    for (size_t i = 1; i < intervals_at_point.size(); ++i)
    {
        TimeInterval &last_merged = merged_intervals.back();
        const TimeInterval &current_interval = intervals_at_point[i];

        if (last_merged.can_merge_with(current_interval))
        {
            last_merged = last_merged.merged_with(current_interval);
        }
        else
        {
            merged_intervals.push_back(current_interval);
        }
    }
    intervals_at_point = merged_intervals;
}

// 添加一条飞行路径的占用信息（优化版本 - 延迟合并策略 + 内存优化）
void OccupancyMap::add_path(const std::string &flight_id, const std::vector<GridNode3D> &path)
{
    if (path.empty())
    {
        return;
    }

    // 使用 unordered_map 缓存待添加的区间，减少重复的合并操作
    std::unordered_map<Point3D, std::vector<TimeInterval>, Point3DHash> pending_intervals;

    int envelope_radius = safety_envelope_ / 2; // 每个方向扩展的网格数
    int half_buffer_int = time_buffer_ / 2;

    // 预估需要处理的点数并预分配空间
    size_t envelope_volume = static_cast<size_t>(safety_envelope_) * safety_envelope_ * safety_envelope_;
    size_t estimated_points = path.size() * envelope_volume;
    pending_intervals.reserve(estimated_points / 4); // 保守估算

    // 第一阶段：收集所有需要添加的时间区间，不立即处理合并
    for (const auto &node : path)
    {
        Point3D center_point = {static_cast<int>(node.x), static_cast<int>(node.y), static_cast<int>(node.z)};
        long long t_node = node.t; // GridNode3D.t 是 long long

        long long start_t = t_node - static_cast<long long>(half_buffer_int);
        long long end_t = t_node + static_cast<long long>(half_buffer_int);

        TimeInterval current_interval(start_t, end_t, flight_id);

        // 遍历安全包络内的所有点
        for (int dx = -envelope_radius; dx <= envelope_radius; ++dx)
        {
            for (int dy = -envelope_radius; dy <= envelope_radius; ++dy)
            {
                for (int dz = -envelope_radius; dz <= envelope_radius; ++dz)
                {
                    Point3D current_point = {std::get<0>(center_point) + dx, std::get<1>(center_point) + dy, std::get<2>(center_point) + dz};

                    // 检查点是否在地图边界内
                    if (std::get<0>(current_point) >= 0 && std::get<0>(current_point) < width_ &&
                        std::get<1>(current_point) >= 0 && std::get<1>(current_point) < height_ &&
                        std::get<2>(current_point) >= 0 && std::get<2>(current_point) < depth_)
                    {
                        // 记录agent占用的点
                        agent_occupancy_[flight_id].insert(current_point);
                        // 将区间添加到待处理列表中
                        pending_intervals[current_point].push_back(current_interval);
                    }
                }
            }
        }
    }

    // 第二阶段：批量处理每个点的所有区间
    for (auto &[point, intervals] : pending_intervals)
    {
        add_intervals_batch(point, intervals);
    }
}

// 检查在特定时间点，某个栅格点是否被占用（优化版本 - 使用 unordered_map）
bool OccupancyMap::check_collision(const Point3D &point, long long timestamp) const
{
    auto it = occupancy_.find(point);
    if (it == occupancy_.end())
    {
        return false; // 该点没有任何占用记录
    }

    const std::vector<TimeInterval> &intervals_at_point = it->second;
    for (const auto &interval : intervals_at_point)
    {
        if (timestamp >= interval.start_time_ && timestamp <= interval.end_time_)
        {
            // 假设时间戳检查是包含端点的
            return true;
        }
    }
    return false;
}

// 移除一个 agent (及其所有相关的占用信息)（优化版本 - 使用 unordered_map）
void OccupancyMap::remove_agent(const std::string &flight_id)
{
    auto agent_it = agent_occupancy_.find(flight_id);
    if (agent_it == agent_occupancy_.end())
    {
        return; // 该 agent 不存在
    }

    const std::unordered_set<Point3D, Point3DHash> &occupied_points = agent_it->second;
    for (const auto &point : occupied_points)
    {
        auto occupancy_it = occupancy_.find(point);
        if (occupancy_it != occupancy_.end())
        {
            std::vector<TimeInterval> &intervals_at_point = occupancy_it->second;

            // 使用 remove_if 和 erase 来移除所有属于该 flight_id 的时间区间
            intervals_at_point.erase(
                std::remove_if(intervals_at_point.begin(), intervals_at_point.end(),
                               [&](const TimeInterval &interval)
                               {
                                   return interval.flight_id_ == flight_id;
                               }),
                intervals_at_point.end());

            // 如果移除后该点的占用列表为空，则从 occupancy_ 映射中移除该点
            if (intervals_at_point.empty())
            {
                occupancy_.erase(occupancy_it);
            }
        }
    }
    // 从 agent_occupancy_ 中移除该 agent
    agent_occupancy_.erase(agent_it);
}

// (可选的 Getter 函数实现)（优化版本 - 使用 unordered_map）
std::vector<TimeInterval> OccupancyMap::get_occupancies_at_point(const Point3D &point) const
{
    auto it = occupancy_.find(point);
    if (it != occupancy_.end())
    {
        return it->second;
    }
    return {}; // 返回空向量
}

std::unordered_set<Point3D, Point3DHash> OccupancyMap::get_agent_occupied_points(const std::string &flight_id) const
{
    auto it = agent_occupancy_.find(flight_id);
    if (it != agent_occupancy_.end())
    {
        return it->second;
    }
    return {}; // 返回空的 unordered_set
}

// 批量添加时间区间（优化版本 v2 - 改进的合并算法）
void OccupancyMap::add_intervals_batch(const Point3D &point, std::vector<TimeInterval> &new_intervals)
{
    if (new_intervals.empty())
    {
        return;
    }

    // 先对新区间进行排序和合并
    std::sort(new_intervals.begin(), new_intervals.end());
    merge_overlapping_intervals_inplace(new_intervals);

    auto &existing_intervals = occupancy_[point];

    // 如果现有区间为空，直接移动新区间
    if (existing_intervals.empty())
    {
        existing_intervals = std::move(new_intervals);
        return;
    }

    // 使用优化的原地合并算法
    merge_sorted_intervals_inplace(existing_intervals, new_intervals);
}

// 合并重叠的时间区间（保留原版本以向后兼容）
void OccupancyMap::merge_overlapping_intervals(std::vector<TimeInterval> &intervals)
{
    if (intervals.size() <= 1)
    {
        return;
    }

    std::vector<TimeInterval> merged;
    merged.reserve(intervals.size());
    merged.push_back(intervals[0]);

    for (size_t i = 1; i < intervals.size(); ++i)
    {
        TimeInterval &last_merged = merged.back();
        const TimeInterval &current = intervals[i];

        if (last_merged.can_merge_with(current))
        {
            last_merged = last_merged.merged_with(current);
        }
        else
        {
            merged.push_back(current);
        }
    }

    intervals = std::move(merged);
}

// 原地合并两个已排序的区间列表（优化版本）
void OccupancyMap::merge_sorted_intervals_inplace(std::vector<TimeInterval> &existing, const std::vector<TimeInterval> &new_intervals)
{
    if (new_intervals.empty())
    {
        return;
    }

    // 为合并结果预留空间
    size_t original_size = existing.size();
    existing.reserve(original_size + new_intervals.size());

    // 在 existing 的末尾插入 new_intervals
    existing.insert(existing.end(), new_intervals.begin(), new_intervals.end());

    // 对整个向量进行原地排序
    std::inplace_merge(existing.begin(), existing.begin() + original_size, existing.end());

    // 原地合并重叠区间
    merge_overlapping_intervals_inplace(existing);
}

// 优化的区间合并算法（原地操作）
void OccupancyMap::merge_overlapping_intervals_inplace(std::vector<TimeInterval> &intervals)
{
    if (intervals.size() <= 1)
    {
        return;
    }

    // 使用双指针技术进行原地合并
    size_t write_pos = 0;

    for (size_t read_pos = 1; read_pos < intervals.size(); ++read_pos)
    {
        if (intervals[write_pos].can_merge_with(intervals[read_pos]))
        {
            // 合并区间
            intervals[write_pos] = intervals[write_pos].merged_with(intervals[read_pos]);
        }
        else
        {
            // 移动到下一个写入位置
            ++write_pos;
            if (write_pos != read_pos)
            {
                intervals[write_pos] = std::move(intervals[read_pos]);
            }
        }
    }

    // 调整容器大小，移除未使用的元素
    intervals.erase(intervals.begin() + write_pos + 1, intervals.end());
}

// 内存预分配和容器优化（第三步优化新增）
void OccupancyMap::reserve_capacity(size_t estimated_agents, size_t estimated_points)
{
    // 为 agent_occupancy_ 预分配空间
    agent_occupancy_.reserve(estimated_agents);

    // 为 occupancy_ 预分配空间
    occupancy_.reserve(estimated_points);

    // 设置负载因子以减少哈希冲突
    // unordered_map 默认负载因子是 1.0，我们设为 0.75 以获得更好的性能
    agent_occupancy_.max_load_factor(0.75f);
    occupancy_.max_load_factor(0.75f);
}
